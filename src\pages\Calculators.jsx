import React, { useEffect, useRef, useState } from 'react'
import SectionHeading from '../components/common/SectionHeading'
import { packagesData } from '../utils/statics'
import MinusSvg from '../components/common/MinusSvg'
import PlusSignSvg from '../components/common/PlusSignSvg'
import { BubbleButton } from '../components/common'

const Calculators = () => {
    const calculatorNavButtons = ['Earnings Calculator', 'Profit / Loss Calculator', 'Risk Calculator']

    const [activeIndex, setActiveIndex] = useState(0);
    const span = useRef(null);
    const container = useRef(null);

    // Earnings Calculator State
    const [earningsData, setEarningsData] = useState({
        accountType: 'Two Step',
        accountSize: 10000,
        profitRate: 8
    });

    // Profit/Loss Calculator State
    const [plData, setPlData] = useState({
        accountSize: 10000,
        entryPrice: 1.0000,
        exitPrice: 1.0100,
        lotSize: 1.0
    });

    // Risk Calculator State
    const [riskData, setRiskData] = useState({
        accountSize: 10000,
        entryPrice: 1.0000,
        exitPrice: 0.9900,
        lotSize: 1.0
    });

    useEffect(() => {
        var clickedBtn = container.current.querySelector(`#button-${activeIndex}`);
        if (clickedBtn && span.current) {
            span.current.style.left = `${clickedBtn.offsetLeft}px`;
            span.current.style.width = `${clickedBtn.offsetWidth}px`;
        }
    }, [activeIndex]);

    // Earnings Calculator Functions
    const calculateEarnings = () => {
        let payout = 0.90; // All account types have 90% payout
        const expectedProfit = earningsData.accountSize * (earningsData.profitRate / 100) * payout;
        return expectedProfit;
    };

    const handleEarningsChange = (field, value) => {
        setEarningsData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Profit/Loss Calculator Functions
    const calculateProfitLoss = () => {
        const pipDifference = (plData.exitPrice - plData.entryPrice) * 10000; // for forex majors
        const pipValue = 10; // $10 per lot per pip
        const profitLoss = pipDifference * plData.lotSize * pipValue;
        const profitLossPercent = (profitLoss / plData.accountSize) * 100;
        return { profitLoss, profitLossPercent };
    };

    const handlePLChange = (field, value) => {
        setPlData(prev => ({
            ...prev,
            [field]: parseFloat(value) || 0
        }));
    };

    // Risk Calculator Functions
    const calculateRisk = () => {
        const pipDifference = Math.abs(riskData.entryPrice - riskData.exitPrice) * 10000; // for 4-decimal pairs
        const pipValue = 10; // typical forex majors
        const riskPerTrade = pipDifference * riskData.lotSize * pipValue;
        const riskPercentage = (riskPerTrade / riskData.accountSize) * 100;
        return { riskPerTrade, riskPercentage };
    };

    const handleRiskChange = (field, value) => {
        setRiskData(prev => ({
            ...prev,
            [field]: parseFloat(value) || 0
        }));
    };

    return (
        <>
            <section className='calculators py-[5rem] pt-[12rem]'>
                <div className="container">
                    <SectionHeading heading="Trading Calculators" para="Lorem ipsum dolor sit amet consectetur. Nec in tortor auctor nec tincidunt eget consectetur. Turpis commodo tortor neque nibh. Eget massa ullamcorper quis tortor commodo nunc id bibendum. Amet proin et aliquet velit in duis." />
                </div>
                <div className="calc-bg">
                    <div className="container">
                        <div className="calculator-box max-w-[860px] mx-auto bg-(--base-color) border-1 border-[#002E55] p-8 rounded-[24px]" ref={container}>

                            <div className="package-navs max-w-[830px] w-full mx-auto mb-9 flex overflow-x-auto">
                                <ul className='flex items-center justify-between relative min-w-max w-full shrink-0 z-[2] border-1 border-[#004986] rounded-full'>
                                    {calculatorNavButtons.map((item, index) => (
                                        <li key={index} className='w-full'><button id={`button-${index}`} onClick={() => setActiveIndex(index)} type='button' className={`text-[18px] text-[#FFFFFF] font-normal cursor-pointer rounded-full w-full py-3 px-4 transition whitespace-nowrap ${activeIndex === index
                                            ? 'active'
                                            : ''
                                            }`}>{item}</button></li>
                                    ))}
                                    <span ref={span} className='bg-border'></span>
                                </ul>
                            </div>

                            <div className="content mt-8">

                                <div className={`${(activeIndex == 0) ? null : 'hidden'}`}>
                                    <h6 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[37px] font-medium text-center'>Earnings Calculator</h6>
                                    <div className="grid grid-cols-1 md:grid-cols-[1.5fr_1fr] gap-6 mt-5">
                                        <div>
                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Account Type</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input
                                                        type="radio"
                                                        name='account_type'
                                                        id='two_step'
                                                        checked={earningsData.accountType === 'Two Step'}
                                                        onChange={() => handleEarningsChange('accountType', 'Two Step')}
                                                    />
                                                    <label htmlFor="two_step">Two Step</label>
                                                </div>
                                                <div className="form-group">
                                                    <input
                                                        type="radio"
                                                        name='account_type'
                                                        id='one_step'
                                                        checked={earningsData.accountType === 'One Step'}
                                                        onChange={() => handleEarningsChange('accountType', 'One Step')}
                                                    />
                                                    <label htmlFor="one_step">One Step</label>
                                                </div>
                                                <div className="form-group">
                                                    <input
                                                        type="radio"
                                                        name='account_type'
                                                        id='instant_funding'
                                                        checked={earningsData.accountType === 'Instant Funding'}
                                                        onChange={() => handleEarningsChange('accountType', 'Instant Funding')}
                                                    />
                                                    <label htmlFor="instant_funding">Instant Funding</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Account Size ($)</p>
                                            <input
                                                type="number"
                                                value={earningsData.accountSize}
                                                onChange={(e) => handleEarningsChange('accountSize', parseFloat(e.target.value) || 0)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4]"
                                                placeholder="Enter account size"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Profit Rate (%)</p>
                                            <input
                                                type="range"
                                                min="1"
                                                max="20"
                                                step="0.1"
                                                value={earningsData.profitRate}
                                                onChange={(e) => handleEarningsChange('profitRate', parseFloat(e.target.value))}
                                                className="w-full h-2 bg-[#004986] rounded-lg appearance-none cursor-pointer slider"
                                            />

                                            <div className="count flex gap-4 items-center mt-4">
                                                <button
                                                    type='button'
                                                    onClick={() => handleEarningsChange('profitRate', Math.max(1, earningsData.profitRate - 0.5))}
                                                    className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer hover:bg-[#004986] transition'
                                                >
                                                    <MinusSvg />
                                                </button>
                                                <p className='text-[#FCFCFC] text-[22px] font-extralight underline min-w-[80px] text-center'>{earningsData.profitRate.toFixed(1)}%</p>
                                                <button
                                                    type='button'
                                                    onClick={() => handleEarningsChange('profitRate', Math.min(20, earningsData.profitRate + 0.5))}
                                                    className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer hover:bg-[#004986] transition'
                                                >
                                                    <PlusSignSvg />
                                                </button>
                                            </div>

                                        </div>
                                        <div>
                                            <div className='bg-[#001C34] rounded-[12px] p-6'>
                                                <h6 className='text-[#FCFCFC] text-[28px] font-medium'>Expected Profit</h6>
                                                <p className='text-[#FCFCFC] text-[19px] font-extralight'>(On up to 90% Profit Split)</p>

                                                <h6 className='text-[30px] md:text-[40px] lg:text-[54px] text-[#FCFCFC] font-medium mb-6'>
                                                    ${calculateEarnings().toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                                </h6>

                                                <div className="mb-6 space-y-2">
                                                    <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                        <span>Account Size:</span>
                                                        <span>${earningsData.accountSize.toLocaleString()}</span>
                                                    </div>
                                                    <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                        <span>Profit Rate:</span>
                                                        <span>{earningsData.profitRate}%</span>
                                                    </div>
                                                    <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                        <span>Payout:</span>
                                                        <span>90%</span>
                                                    </div>
                                                </div>

                                                <BubbleButton Tag='a' target='_blank' href='https://my.go4trades.com/login?_gl=1*1x22s31*_ga******************************_ga_2HWEFCWNKC*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw*_ga_SS36ZZJDJK*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw' className='my-btn text-white !font-normal bg-blue-gradient hover:opacity-60 transition inline-block !text-[16px] !py-2.5 w-full text-center'>Get Funded!</BubbleButton>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className={`${(activeIndex == 1) ? null : 'hidden'}`}>
                                    <h6 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[37px] font-medium text-center'>Profit / Loss Calculator</h6>
                                    <div className="grid grid-cols-1 md:grid-cols-[1.5fr_1fr] gap-6 mt-5">
                                        <div>
                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Account Size ($)</p>
                                            <input
                                                type="number"
                                                value={plData.accountSize}
                                                onChange={(e) => handlePLChange('accountSize', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4] mb-4"
                                                placeholder="Enter account size"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Entry Price</p>
                                            <input
                                                type="number"
                                                step="0.0001"
                                                value={plData.entryPrice}
                                                onChange={(e) => handlePLChange('entryPrice', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4] mb-4"
                                                placeholder="Enter entry price"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Exit Price</p>
                                            <input
                                                type="number"
                                                step="0.0001"
                                                value={plData.exitPrice}
                                                onChange={(e) => handlePLChange('exitPrice', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4] mb-4"
                                                placeholder="Enter exit price"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Lot Size</p>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={plData.lotSize}
                                                onChange={(e) => handlePLChange('lotSize', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4]"
                                                placeholder="Enter lot size"
                                            />

                                        </div>
                                        <div>
                                            <div className='bg-[#001C34] rounded-[12px] p-6'>
                                                <h6 className='text-[#FCFCFC] text-[28px] font-medium'>Profit / Loss Result</h6>
                                                <p className='text-[#FCFCFC] text-[19px] font-extralight mb-4'>Calculation based on forex majors</p>

                                                {(() => {
                                                    const result = calculateProfitLoss();
                                                    const isProfit = result.profitLoss >= 0;
                                                    return (
                                                        <>
                                                            <h6 className={`text-[30px] md:text-[40px] lg:text-[54px] font-medium mb-4 ${isProfit ? 'text-[#80DBB4]' : 'text-[#BE1320]'}`}>
                                                                {isProfit ? '+' : ''}${result.profitLoss.toFixed(2)}
                                                            </h6>

                                                            <div className="mb-6 space-y-2">
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>P/L Percentage:</span>
                                                                    <span className={isProfit ? 'text-[#80DBB4]' : 'text-[#BE1320]'}>
                                                                        {isProfit ? '+' : ''}{result.profitLossPercent.toFixed(2)}%
                                                                    </span>
                                                                </div>
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>Pip Difference:</span>
                                                                    <span>{((plData.exitPrice - plData.entryPrice) * 10000).toFixed(1)} pips</span>
                                                                </div>
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>Account Size:</span>
                                                                    <span>${plData.accountSize.toLocaleString()}</span>
                                                                </div>
                                                            </div>
                                                        </>
                                                    );
                                                })()}

                                                <BubbleButton Tag='a' target='_blank' href='https://my.go4trades.com/login?_gl=1*1x22s31*_ga******************************_ga_2HWEFCWNKC*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw*_ga_SS36ZZJDJK*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw' className='my-btn text-white !font-normal bg-blue-gradient hover:opacity-60 transition inline-block !text-[16px] !py-2.5 w-full text-center'>Get Funded!</BubbleButton>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className={`${(activeIndex == 2) ? null : 'hidden'}`}>
                                    <h6 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[37px] font-medium text-center'>Risk Calculator</h6>
                                    <div className="grid grid-cols-1 md:grid-cols-[1.5fr_1fr] gap-6 mt-5">
                                        <div>
                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Account Size ($)</p>
                                            <input
                                                type="number"
                                                value={riskData.accountSize}
                                                onChange={(e) => handleRiskChange('accountSize', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4] mb-4"
                                                placeholder="Enter account size"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Entry Price</p>
                                            <input
                                                type="number"
                                                step="0.0001"
                                                value={riskData.entryPrice}
                                                onChange={(e) => handleRiskChange('entryPrice', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4] mb-4"
                                                placeholder="Enter entry price"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Stop Loss Price</p>
                                            <input
                                                type="number"
                                                step="0.0001"
                                                value={riskData.exitPrice}
                                                onChange={(e) => handleRiskChange('exitPrice', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4] mb-4"
                                                placeholder="Enter stop loss price"
                                            />

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Lot Size</p>
                                            <input
                                                type="number"
                                                step="0.01"
                                                value={riskData.lotSize}
                                                onChange={(e) => handleRiskChange('lotSize', e.target.value)}
                                                className="w-full bg-[#001C34] border border-[#004986] rounded-[8px] px-4 py-3 text-[#FCFCFC] text-[18px] focus:outline-none focus:border-[#80DBB4]"
                                                placeholder="Enter lot size"
                                            />

                                        </div>
                                        <div>
                                            <div className='bg-[#001C34] rounded-[12px] p-6'>
                                                <h6 className='text-[#FCFCFC] text-[28px] font-medium'>Risk Analysis</h6>
                                                <p className='text-[#FCFCFC] text-[19px] font-extralight mb-4'>Risk per trade calculation</p>

                                                {(() => {
                                                    const result = calculateRisk();
                                                    return (
                                                        <>
                                                            <h6 className='text-[30px] md:text-[40px] lg:text-[54px] text-[#BE1320] font-medium mb-4'>
                                                                ${result.riskPerTrade.toFixed(2)}
                                                            </h6>

                                                            <div className="mb-6 space-y-2">
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>Risk Percentage:</span>
                                                                    <span className='text-[#BE1320]'>{result.riskPercentage.toFixed(2)}%</span>
                                                                </div>
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>Pip Difference:</span>
                                                                    <span>{(Math.abs(riskData.entryPrice - riskData.exitPrice) * 10000).toFixed(1)} pips</span>
                                                                </div>
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>Account Size:</span>
                                                                    <span>${riskData.accountSize.toLocaleString()}</span>
                                                                </div>
                                                                <div className="flex justify-between text-[#CCCCCC] text-[16px]">
                                                                    <span>Lot Size:</span>
                                                                    <span>{riskData.lotSize}</span>
                                                                </div>
                                                            </div>

                                                            {result.riskPercentage > 2 && (
                                                                <div className="mb-4 p-3 bg-[#BE1320] bg-opacity-20 border border-[#BE1320] rounded-[8px]">
                                                                    <p className="text-[#BE1320] text-[14px]">⚠️ High Risk: Consider reducing lot size or adjusting stop loss</p>
                                                                </div>
                                                            )}
                                                        </>
                                                    );
                                                })()}

                                                <BubbleButton Tag='a' target='_blank' href='https://my.go4trades.com/login?_gl=1*1x22s31*_ga******************************_ga_2HWEFCWNKC*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw*_ga_SS36ZZJDJK*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw' className='my-btn text-white !font-normal bg-blue-gradient hover:opacity-60 transition inline-block !text-[16px] !py-2.5 w-full text-center'>Get Funded!</BubbleButton>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}

export default Calculators