import React, { useEffect, useRef, useState } from 'react'
import SectionHeading from '../components/common/SectionHeading'
import { packagesData } from '../utils/statics'
import MinusSvg from '../components/common/MinusSvg'
import PlusSignSvg from '../components/common/PlusSignSvg'
import { BubbleButton } from '../components/common'

const Calculators = () => {
    const calculatorNavButtons = ['Earnings Calculator', 'Profit / Loss Calculator', 'Risk Calculator']

    const [activeIndex, setActiveIndex] = useState(0);
    const span = useRef(null);
    const container = useRef(null);
    useEffect(() => {
        var clickedBtn = container.current.querySelector(`#button-${activeIndex}`);
        if (clickedBtn && span.current) {
            span.current.style.left = `${clickedBtn.offsetLeft}px`;
            span.current.style.width = `${clickedBtn.offsetWidth}px`;
        }
    }, [activeIndex]);

    return (
        <>
            <section className='calculators py-[5rem] pt-[12rem]'>
                <div className="container">
                    <SectionHeading heading="Trading Calculators" para="Lorem ipsum dolor sit amet consectetur. Nec in tortor auctor nec tincidunt eget consectetur. Turpis commodo tortor neque nibh. Eget massa ullamcorper quis tortor commodo nunc id bibendum. Amet proin et aliquet velit in duis." />
                </div>
                <div className="calc-bg">
                    <div className="container">
                        <div className="calculator-box max-w-[860px] mx-auto bg-(--base-color) border-1 border-[#002E55] p-8 rounded-[24px]" ref={container}>

                            <div className="package-navs max-w-[830px] w-full mx-auto mb-9 flex overflow-x-auto">
                                <ul className='flex items-center justify-between relative min-w-max w-full shrink-0 z-[2] border-1 border-[#004986] rounded-full'>
                                    {calculatorNavButtons.map((item, index) => (
                                        <li key={index} className='w-full'><button id={`button-${index}`} onClick={() => setActiveIndex(index)} type='button' className={`text-[18px] text-[#FFFFFF] font-normal cursor-pointer rounded-full w-full py-3 px-4 transition whitespace-nowrap ${activeIndex === index
                                            ? 'active'
                                            : ''
                                            }`}>{item}</button></li>
                                    ))}
                                    <span ref={span} className='bg-border'></span>
                                </ul>
                            </div>

                            <div className="content mt-8">

                                <div className={`${(activeIndex == 0) ? null : 'hidden'}`}>
                                    <h6 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[37px] font-medium text-center'>Earnings Calculator</h6>
                                    <div className="grid grid-cols-1 md:grid-cols-[1.5fr_1fr] gap-6 mt-5">
                                        <div>
                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Account</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='two_phase' />
                                                    <label htmlFor="two_phase">Two phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='one_phase' />
                                                    <label htmlFor="one_phase">One phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='vip' />
                                                    <label htmlFor="vip">VIP</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Account Size</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='two_phase' />
                                                    <label htmlFor="two_phase">Two phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='one_phase' />
                                                    <label htmlFor="one_phase">One phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='vip' />
                                                    <label htmlFor="vip">VIP</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Profit Rate</p>
                                            <input type="range" name="profile_rate" id="profile_rate" />

                                            <div className="count flex gap-4 items-center mt-4">
                                                <button type='button' className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer'><MinusSvg /></button>
                                                <p className='text-[#FCFCFC] text-[22px] font-extralight underline'>8000</p>
                                                <button type='button' className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer'><PlusSignSvg /></button>
                                            </div>

                                        </div>
                                        <div>
                                            <div className='bg-[#001C34] rounded-[12px] p-6'>
                                                <h6 className='text-[#FCFCFC] text-[28px] font-medium'>Take Home</h6>
                                                <p className='text-[#FCFCFC] text-[19px] font-extralight'>(On up to 90% Profit Split)</p>

                                                <h6 className='text-[30px] md:text-[40px] lg:text-[54px] text-[#FCFCFC] font-medium mb-10'>$1,935<span className='text-[#80DBB4] text-[20px] font-normal'>/month</span></h6>

                                                <BubbleButton Tag='a' target='_blank' href='https://my.go4trades.com/login?_gl=1*1x22s31*_ga*MTE5MDgxNzk5OS4xNzUxNjE 1 ODQz*_ga_2HWEFCWNKC*czE3NTIwNzE4NjUkb zIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw*_ga_ SS36ZZJDJK*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw' className='my-btn text-white !font-normal bg-blue-gradient hover:opacity-60 transition inline-block !text-[16px] !py-2.5'>Get Funded!</BubbleButton>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className={`${(activeIndex == 1) ? null : 'hidden'}`}>
                                    <h6 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[37px] font-medium text-center'>Profit / Loss Calculator</h6>
                                    <div className="grid grid-cols-1 md:grid-cols-[1.5fr_1fr] gap-6 mt-5">
                                        <div>
                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Account</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='two_phase' />
                                                    <label htmlFor="two_phase">Two phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='one_phase' />
                                                    <label htmlFor="one_phase">One phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='vip' />
                                                    <label htmlFor="vip">VIP</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Account Size</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='two_phase' />
                                                    <label htmlFor="two_phase">Two phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='one_phase' />
                                                    <label htmlFor="one_phase">One phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='vip' />
                                                    <label htmlFor="vip">VIP</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Profit Rate</p>
                                            <input type="range" name="profile_rate" id="profile_rate" />

                                            <div className="count flex gap-4 items-center mt-4">
                                                <button type='button' className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer'><MinusSvg /></button>
                                                <p className='text-[#FCFCFC] text-[22px] font-extralight underline'>8000</p>
                                                <button type='button' className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer'><PlusSignSvg /></button>
                                            </div>

                                        </div>
                                        <div>
                                            <div className='bg-[#001C34] rounded-[12px] p-6'>
                                                <h6 className='text-[#FCFCFC] text-[28px] font-medium'>Take Home</h6>
                                                <p className='text-[#FCFCFC] text-[19px] font-extralight'>(On up to 90% Profit Split)</p>

                                                <h6 className='text-[30px] md:text-[40px] lg:text-[54px] text-[#FCFCFC] font-medium mb-10'>$1,935<span className='text-[#80DBB4] text-[20px] font-normal'>/month</span></h6>

                                                <BubbleButton Tag='a' target='_blank' href='https://my.go4trades.com/login?_gl=1*1x22s31*_ga*MTE5MDgxNzk5OS4xNzUxNjE 1 ODQz*_ga_2HWEFCWNKC*czE3NTIwNzE4NjUkb zIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw*_ga_ SS36ZZJDJK*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw' className='my-btn text-white !font-normal bg-blue-gradient hover:opacity-60 transition inline-block !text-[16px] !py-2.5'>Get Funded!</BubbleButton>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className={`${(activeIndex == 2) ? null : 'hidden'}`}>
                                    <h6 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[37px] font-medium text-center'>Risk Calculator</h6>
                                    <div className="grid grid-cols-1 md:grid-cols-[1.5fr_1fr] gap-6 mt-5">
                                        <div>
                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4'>Account</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='two_phase' />
                                                    <label htmlFor="two_phase">Two phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='one_phase' />
                                                    <label htmlFor="one_phase">One phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account' id='vip' />
                                                    <label htmlFor="vip">VIP</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Account Size</p>
                                            <div className="flex flex-wrap gap-4">
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='two_phase' />
                                                    <label htmlFor="two_phase">Two phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='one_phase' />
                                                    <label htmlFor="one_phase">One phase</label>
                                                </div>
                                                <div className="form-group">
                                                    <input type="radio" name='account_size' id='vip' />
                                                    <label htmlFor="vip">VIP</label>
                                                </div>
                                            </div>

                                            <p className='text-[#FCFCFC] text-[20px] font-extralight mb-4 mt-6'>Profit Rate</p>
                                            <input type="range" name="profile_rate" id="profile_rate" />

                                            <div className="count flex gap-4 items-center mt-4">
                                                <button type='button' className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer'><MinusSvg /></button>
                                                <p className='text-[#FCFCFC] text-[22px] font-extralight underline'>8000</p>
                                                <button type='button' className='border-[#004986] border-1 w-[45px] h-[45px] rounded-full text-[#80DBB4] text-[16px] font-bold flex all-center cursor-pointer'><PlusSignSvg /></button>
                                            </div>

                                        </div>
                                        <div>
                                            <div className='bg-[#001C34] rounded-[12px] p-6'>
                                                <h6 className='text-[#FCFCFC] text-[28px] font-medium'>Take Home</h6>
                                                <p className='text-[#FCFCFC] text-[19px] font-extralight'>(On up to 90% Profit Split)</p>

                                                <h6 className='text-[30px] md:text-[40px] lg:text-[54px] text-[#FCFCFC] font-medium mb-10'>$1,935<span className='text-[#80DBB4] text-[20px] font-normal'>/month</span></h6>

                                                <BubbleButton Tag='a' target='_blank' href='https://my.go4trades.com/login?_gl=1*1x22s31*_ga*MTE5MDgxNzk5OS4xNzUxNjE 1 ODQz*_ga_2HWEFCWNKC*czE3NTIwNzE4NjUkb zIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw*_ga_ SS36ZZJDJK*czE3NTIwNzE4NjUkbzIkZzEkdDE3NTIwNzIwMTIkajYwJGwwJGgw' className='my-btn text-white !font-normal bg-blue-gradient hover:opacity-60 transition inline-block !text-[16px] !py-2.5'>Get Funded!</BubbleButton>

                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}

export default Calculators