import { useState } from 'react';
const AcedemyCheckBox = (props) => {
    const [isChecked, setIsChecked] = useState(false);

    const handleCheckboxChange = (e) => {
        setIsChecked(e.target.checked);
    };

    return (
        <div className={`acedemy-checkbox px-4 py-4 rounded-[12px] min-h-[260px] flex flex-col justify-between relative ${isChecked ? 'gradient-1' : 'gradient-2 gradient-2 border border-[#A0A0A0]'} `}>
            <label class="ios-checkbox purple">
                <input type="checkbox" onChange={handleCheckboxChange} />
                <div class="checkbox-wrapper">
                    <div class="checkbox-bg"></div>
                    <svg fill="none" viewBox="0 0 24 24" class="checkbox-icon">
                        <path
                            stroke-linejoin="round"
                            stroke-linecap="round"
                            stroke-width="3"
                            stroke="currentColor"
                            d="M4 12L10 18L20 6"
                            class="check-path"
                        ></path>
                    </svg>
                </div>
            </label>
            <div className="image">
                <img src={props.img} alt="image" />
            </div>
            <div className="text">
                <h5 className={`text-[24px] font-medium ${isChecked ? 'text-white' : 'text-(--base-color)'}`}>{props.heading}</h5>
                <p className={`text-[16px] font-light ${isChecked ? 'text-[#FCFCFC]' : 'text-(--base-color)'}`}>{props.para}</p>
            </div>
        </div>
    )
}

export default AcedemyCheckBox