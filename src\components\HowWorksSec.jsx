import React, { useEffect, useRef } from 'react'
import SectionHeading from './common/SectionHeading'
import { HowWorks, HowWorks1, HowWorks2 } from '../assets'
import { BubbleButton, SidebarPopup } from './common'
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

const HowWorksSec = ({ handleScroll }) => {
    let mainContainer = useRef();
    let box1 = useRef();
    let box2 = useRef();
    let box3 = useRef();
    useEffect(() => {
        let ctx = gsap.context(() => {
            // Initial setup - hide all sections except the first one
            gsap.set([box2.current, box3.current], {
                opacity: 0,
                pointerEvents: 'none',
                y: 50,
                scale: 0.95
            });

            gsap.set(box1.current, {
                opacity: 1,
                pointerEvents: 'auto',
                y: 0,
                scale: 1
            });

            // Create main timeline with smooth scroll trigger
            const master = gsap.timeline({
                scrollTrigger: {
                    trigger: mainContainer.current,
                    start: 'top top',
                    end: '+=4000', // Increased for even smoother transitions
                    pin: true,
                    scrub: 1.5, // Slower scrub for ultra-smooth feel
                    anticipatePin: 1,
                    refreshPriority: -1,
                    invalidateOnRefresh: true,
                    onUpdate: (self) => {
                        const progress = self.progress;

                        // Add smooth background color transitions
                        const container = mainContainer.current;
                        if (container) {
                            if (progress < 0.33) {
                                // Learn section - blue tint
                                gsap.to(container, {
                                    backgroundColor: 'rgba(1, 17, 31, 0.95)',
                                    duration: 0.3
                                });
                            } else if (progress < 0.66) {
                                // Practice section - green tint
                                gsap.to(container, {
                                    backgroundColor: 'rgba(1, 25, 17, 0.95)',
                                    duration: 0.3
                                });
                            } else {
                                // Earn section - purple tint
                                gsap.to(container, {
                                    backgroundColor: 'rgba(17, 1, 31, 0.95)',
                                    duration: 0.3
                                });
                            }
                        }
                    }
                }
            });

            // Transition 1: Learn to Practice (smoother timing)
            master
                .to(box1.current, {
                    opacity: 0,
                    y: -40,
                    scale: 0.9,
                    rotationX: -10,
                    pointerEvents: 'none',
                    duration: 1.2,
                    ease: 'power3.inOut'
                }, 0)
                .to(box2.current, {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    rotationX: 0,
                    pointerEvents: 'auto',
                    duration: 1.2,
                    ease: 'power3.inOut'
                }, 0.4);

            // Transition 2: Practice to Earn (smoother timing)
            master
                .to(box2.current, {
                    opacity: 0,
                    y: -40,
                    scale: 0.9,
                    rotationX: -10,
                    pointerEvents: 'none',
                    duration: 1.2,
                    ease: 'power3.inOut'
                }, 2)
                .to(box3.current, {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    rotationX: 0,
                    pointerEvents: 'auto',
                    duration: 1.2,
                    ease: 'power3.inOut'
                }, 2.4);

            // Add subtle image animations and text effects
            const images = [
                box1.current?.querySelector('.image'),
                box2.current?.querySelector('.image'),
                box3.current?.querySelector('.image')
            ].filter(Boolean);

            const textElements = [
                box1.current?.querySelector('.text'),
                box2.current?.querySelector('.text'),
                box3.current?.querySelector('.text')
            ].filter(Boolean);

            images.forEach((img, index) => {
                if (img) {
                    gsap.set(img, { transformOrigin: 'center center' });

                    // Add floating animation
                    gsap.to(img, {
                        y: -10,
                        duration: 2.5,
                        ease: 'power2.inOut',
                        yoyo: true,
                        repeat: -1,
                        delay: index * 0.7
                    });

                    // Add subtle rotation on hover
                    img.addEventListener('mouseenter', () => {
                        gsap.to(img, {
                            rotation: 5,
                            scale: 1.05,
                            duration: 0.3,
                            ease: 'power2.out'
                        });
                    });

                    img.addEventListener('mouseleave', () => {
                        gsap.to(img, {
                            rotation: 0,
                            scale: 1,
                            duration: 0.3,
                            ease: 'power2.out'
                        });
                    });
                }
            });

            // Add stagger animation to text elements when they become visible
            textElements.forEach((text, index) => {
                if (text) {
                    const children = text.children;
                    gsap.set(children, { y: 30, opacity: 0 });

                    // Create timeline for text reveal
                    const textTl = gsap.timeline({ paused: true });
                    textTl.to(children, {
                        y: 0,
                        opacity: 1,
                        duration: 0.6,
                        stagger: 0.1,
                        ease: 'power2.out'
                    });

                    // Trigger text animation based on section visibility
                    if (index === 0) {
                        textTl.play(); // First section starts visible
                    }
                }
            });

            // Handle resize events
            const handleResize = () => {
                ScrollTrigger.refresh();
            };

            window.addEventListener('resize', handleResize);

            // Refresh ScrollTrigger after component mount
            setTimeout(() => {
                ScrollTrigger.refresh();
            }, 100);

            return () => {
                window.removeEventListener('resize', handleResize);
            };
        }, mainContainer);

        return () => ctx.revert();
    }, [])
    
    const [isSidebarOpen, setIsSidebarOpen] = React.useState(false);
    const [selectedLevel, setSelectedLevel] = React.useState('');

    const handleLevelSelect = (level) => {
        console.log('Level selected:', level);
        setSelectedLevel(level);
        setIsSidebarOpen(true);
        console.log('Sidebar should be open now, isSidebarOpen:', true);
    };

    const handleCloseSidebar = () => {
        setIsSidebarOpen(false);
        setSelectedLevel('');
    };
    return (
        <div>
            <section className='how-works py-[5rem]' ref={mainContainer}>
                <div className="container">
                    <SectionHeading heading="How does this work?" />
                    <div className="relative flex flex-col justify-center max-w-[1280px] mx-auto min-h-[550px] max-md:min-h-[500px] rounded-[32px] border-[2px] border-[#002E55] bg-[#01111FB2] max-lg:p-10 how-works-box">
                        <div className='grid grid-cols-1 lg:grid-cols-2 absolute top-1/2 left-0 -translate-y-1/2 max-md:px-6' ref={box1}>
                            <div>
                                <div className="image max-lg:w-[50%] mx-auto w-max relative z-[2]">
                                    <img src={HowWorks} alt="image" />
                                    <div class="dots">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                            <div className="text lg:pr-[8rem]">
                                <h4 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[44px] font-medium'>Learn </h4>
                                <p className='text-[#FCFCFC] text-[19px] md:text-[25px] lg:text-[32px] font-light mb-4'>KHDA-approved trading programs made simple from beginner to advanced, get the skills that matter. </p>
                                <BubbleButton Tag='button' onClick={() => handleLevelSelect('get-started')} className='my-btn text-white font-medium bg-blue-gradient hover:opacity-60 transition inline-block'>Get started </BubbleButton>
                            </div>
                        </div>
                        <div className='grid grid-cols-1 lg:grid-cols-2 absolute top-1/2 left-0 -translate-y-1/2 max-md:px-6' ref={box2}>
                            <div>
                                <div className="image max-lg:w-[50%] mx-auto w-max relative z-[2]">
                                    <img src={HowWorks1} alt="image" />
                                </div>
                            </div>
                            <div className="text lg:pr-[8rem]">
                                <h4 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[44px] font-medium'>Practice</h4>
                                <p className='text-[#FCFCFC] text-[19px] md:text-[25px] lg:text-[32px] font-light mb-4'>Test your skills with simulated capital. No pressure, no risk Just virtual capital and realistic challenges to help you build confidence.</p>
                                <BubbleButton Tag='a' href='/challenges' className='my-btn text-white font-medium bg-blue-gradient hover:opacity-60 transition inline-block'>View Challenges</BubbleButton>
                            </div>
                        </div>
                        <div className='grid grid-cols-1 lg:grid-cols-2 absolute top-1/2 left-0 -translate-y-1/2 items-center max-md:px-6' ref={box3}>
                            <div>
                                <div className="image max-lg:w-[50%] mx-auto w-max relative z-[2]">
                                    <img src={HowWorks2} alt="image" />
                                </div>
                            </div>
                            <div className="text lg:pr-[8rem]">
                                <h4 className='text-[#FCFCFC] text-[25px] md:text-[30px] lg:text-[44px] font-medium'>Earn</h4>
                                <p className='text-[#FCFCFC] text-[19px] md:text-[25px] lg:text-[32px] font-light mb-4'>Earn as you Grow Join funded challenges where you keep up to 90% of your rewards.</p>
                                <BubbleButton Tag='a' href='/scaling-plan' onClick={handleScroll} className='my-btn text-white font-medium bg-blue-gradient hover:opacity-60 transition inline-block'>Try Scaling Plan</BubbleButton>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Sidebar Popup with Tally Form */}
            <SidebarPopup
                isOpen={isSidebarOpen}
                onClose={handleCloseSidebar}
                selectedLevel={selectedLevel}
            />
        </div>
    )
}

export default HowWorksSec


