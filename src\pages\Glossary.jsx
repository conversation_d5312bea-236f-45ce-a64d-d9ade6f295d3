import React, { useEffect, useRef, useState } from 'react'
import SectionHeading from '../components/common/SectionHeading'
import { generalRules } from '../utils/statics';

const Glossary = () => {

    const alphabets = Array.from({ length: 21 }, (_, i) => String.fromCharCode(97 + i));

    const [activeIndex, setActiveIndex] = useState(0);
    const span = useRef(null);
    const container = useRef(null);

    useEffect(() => {

        var clickedBtn = container.current.querySelector(`#button-${activeIndex}`);
        if (clickedBtn && span.current) {
            span.current.style.left = `${clickedBtn.offsetLeft}px`;
            span.current.style.width = `${clickedBtn.offsetWidth}px`;
        }

    }, [activeIndex])

    return (
        <section className='glossary pt-[12rem]' ref={container}>
            <div className="container">
                <SectionHeading heading="Glossary" />

                <div className="package-navs w-full mx-auto mb-20 flex overflow-x-auto">
                    <ul className='flex items-center justify-between relative min-w-max w-full shrink-0 z-[2] border-1 border-[#004986] rounded-full'>
                        {alphabets.map((item, index) => (
                            <li key={index} className='w-full'><button id={`button-${index}`} onClick={() => setActiveIndex(index)} type='button' className={`text-[12px] sm:text-[16px] lg:text-[22px] text-[#FFFFFF] font-normal cursor-pointer rounded-full w-full py-3 px-2 md:px-4 transition uppercase ${activeIndex === index
                                ? 'active'
                                : ''
                                }`}>{item}</button></li>
                        ))}
                        <span ref={span} className='bg-border'></span>
                    </ul>
                </div>

                <div className="content-main-box">
                    {alphabets.map((item, index) => (
                        <div key={index} className={`content ${(activeIndex == index) ? '' : 'hidden'}`}>
                            <h2 className='mb-7 uppercase'>{item}</h2>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Ask Price:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>The lowest price a seller is willing to accept for an asset.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Assets:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Any resource with economic value, especially tradable instruments like stocks, forex pairs, or commodities.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Account Balance:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>The total amount of money in a trading account excluding open trades.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Allocation:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Distribution of capital across various instruments or sectors.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Arbitrage:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Exploiting price differences of the same asset in different markets.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>ATR (Average True Range):</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>A volatility indicator.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>API (Application Programming Interface):</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Allows automated trading and data sharing.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>AUM (Assets Under Management):</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>The total market value managed by a firm.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Auto Trading:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Trading executed by algorithms or bots.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Altcoins:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Cryptocurrencies other than Bitcoin.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>ASIC:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Regulatory body in Australia; important for broker licensing.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Alpha:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>The excess return on an investment relative to the market.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Ask-Bid Spread:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>The difference between ask and bid prices.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Asset Class:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight mb-7'>Categories of assets like equities, forex, crypto, or commodities.</p>

                            <h4 className='text-[#FCFCFC] text-[20px] md:text-[24px] font-medium mb-2'>Average Fill Price:</h4>
                            <p className='text-[#FCFCFC] text-[16px] md:text-[18px] font-extralight'>The average price at which a trade is executed.</p>

                        </div>
                    ))}

                </div>

            </div>
        </section>
    )
}

export default Glossary